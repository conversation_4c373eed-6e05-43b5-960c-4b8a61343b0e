import pandas as pd

from src.domain.analyze.aggregate.analysis.utils import AnalyzeDataSpec


class AnalyzeZhStockFundFlowAggregate:
    
    def __init__(self, code: str):
        self.code = code
        
        
    def start_zh_smart_money_vs_noise_trader_flow(self):
        pass
    
    def start_zh_major_player_entry_signal(self):
        pass
    
    
    def start_zh_volume_inertia_cluster(self):
        pass
    
    
    def start_zh_phase_bottom(self):
        pass
    

class AnalyzeZhSectorFundFlowAggregate:
    
    def start_zh_sector_fund_flow(self):
        pass
    
    

# 主力散户线分析
class _ZhSmartMoneyVsNoiseTraderFlowAggregate:
    pass


# 主力入场信号
class _ZhMajorPlayerEntrySignalAggregate(AnalyzeDataSpec):
    
    def __init__(self, code: str):
        self.code = code
    
    
    def analyze(self):
        # 数据预处理
        df_kline["日期"] = pd.to_datetime(df_kline["trade_date"], format="%Y%m%d")
        df_funds["日期"] = pd.to_datetime(df_funds["日期"], format="%Y-%m-%d")
        df = pd.merge(df_kline, df_funds, on="日期", how="inner")

        df["pct_chg"] = pd.to_numeric(df["pct_chg"], errors="coerce")
        df["vol"] = pd.to_numeric(df["vol"], errors="coerce")
        df["主力净流入-净占比"] = pd.to_numeric(df["主力净流入-净占比"], errors="coerce")

        threshold_75 = df["主力净流入-净占比"].quantile(0.75)

        # 信号计算
        df["强势介入"] = (df["主力净流入-净额"] > 0) & (df["主力净流入-净占比"] > threshold_75)
        df["异动不涨"] = df["强势介入"] & (df["pct_chg"].abs() < 1.2)
        threshold_drop = 2.0
        df["下跌放缓"] = df["强势介入"] & (df["pct_chg"] < 0) & (df["pct_chg"] > -threshold_drop)
        df["信号"] = ((df["异动不涨"]) | (df["下跌放缓"])).astype(int)

        n = 2
        df["连续信号数"] = df["信号"].rolling(window=n).sum().fillna(0).astype(int)
        df["主力入场信号"] = df["连续信号数"] == n

        df_sorted = df.sort_values(by="日期", ascending=True)

        # 准备图表数据
        kline_data = [
            [
                float(df_sorted["open"].iloc[i]),
                float(df_sorted["close"].iloc[i]),
                float(df_sorted["low"].iloc[i]),
                float(df_sorted["high"].iloc[i])
            ]
            for i in range(len(df_sorted))
        ]
        date_list = df_sorted["日期"].dt.strftime("%Y-%m-%d").tolist()
        bar_data = df_sorted["vol"].tolist()
        line_data = df_sorted["主力净流入-净占比"].tolist()

        # 分类信号数据
        abnormal_no_rise_data = [
            float(df_sorted["low"].iloc[i]) - 0.05 if (
                    df_sorted["主力入场信号"].iloc[i] and df_sorted["异动不涨"].iloc[i]) else None
            for i in range(len(df_sorted))
        ]

        decline_slow_data = [
            float(df_sorted["low"].iloc[i]) - 0.05 if (
                    df_sorted["主力入场信号"].iloc[i] and df_sorted["下跌放缓"].iloc[i] and not
            df_sorted["异动不涨"].iloc[i]) else None
            for i in range(len(df_sorted))
        ]

        mixed_signal_data = [
            float(df_sorted["low"].iloc[i]) - 0.05 if (
                    df_sorted["主力入场信号"].iloc[i] and df_sorted["异动不涨"].iloc[i] and
                    df_sorted["下跌放缓"].iloc[i]) else None
            for i in range(len(df_sorted))
        ]
    
    def make_graph(self):
        pass


# 量能堆积
class _ZhVolumeInertiaClusterAggregate:
    pass


# 阶段性底部
class _ZhPhaseBottomAggregate:
    pass


# 板块资金流
class _ZhSectorFundFlowAggregate:
    pass

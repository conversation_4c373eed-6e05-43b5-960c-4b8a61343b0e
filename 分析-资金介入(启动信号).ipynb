#%%
# 该方法可能能识别主升浪的前夕
#%%

import pandas as pd
code = "600372"
kline_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_daily_data.csv"
funds_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_individual_fund_flow.csv"

df_kline = pd.read_csv(kline_path)
# 读取资金流数据
df_funds = pd.read_csv(funds_path)


#%%
import pandas as pd

def compute_main_ratio_thresholds(kline_csv: str, funds_csv: str):
    """
    计算过去 6 个月内，“主力净占比” 的 50%、75%、90% 三个参考分位值。

    参数：
    - kline_csv: 新的 K 线数据 CSV 路径（包含 trade_date, close, pct_chg, ...）
    - funds_csv: 资金流数据 CSV 路径（包含 日期, 主力净流入-净额, 主力净流入-净占比, ...）

    返回：
    - 一个字典，包含 '50%', '75%', '90%' 对应的百分位参考值（单位：%）
    """

    # 1. 读取 K 线数据（跳过首行多余的 header）
    df_kline = pd.read_csv(kline_csv)
    # 假设 K 线里有列 'trade_date'（格式 YYYYMMDD），再加上其他字段
    # 先转换日期
    df_kline['日期'] = pd.to_datetime(df_kline['trade_date'], format='%Y%m%d')

    # 2. 读取资金流数据
    df_funds = pd.read_csv(funds_csv)
    # 假设资金流里有一列 '日期'（格式 YYYY-MM-DD）、'主力净流入-净占比' 等
    df_funds['日期'] = pd.to_datetime(df_funds['日期'], format='%Y-%m-%d')

    # 3. 合并两个 DataFrame，按“日期”列 inner join
    df = pd.merge(df_kline, df_funds, on='日期', how='inner')

    # 4. 取最近 6 个月的数据
    latest_date = df['日期'].max()
    six_months_ago = latest_date - pd.DateOffset(months=6)
    df_recent = df[df['日期'] >= six_months_ago].copy()

    # 5. 确保"主力净占比"列是数值类型（单位是百分比，如 5.39 表示 5.39%）
    df_recent['主力净流入-净占比'] = pd.to_numeric(df_recent['主力净流入-净占比'], errors='coerce')
    # 6. 计算 50%、75%、90% 分位数
    percentiles = df_recent['主力净流入-净占比'].quantile([0.50, 0.75, 0.90])
    # 将 index 改为便于查看的字符串
    percentiles.index = ['50%', '75%', '90%']

    # 7. 转换成字典并返回
    thresholds = {
        '50%': float(percentiles['50%']),
        '75%': float(percentiles['75%']),
        '90%': float(percentiles['90%']),
    }
    return thresholds



result = compute_main_ratio_thresholds(kline_path, funds_path)
print("过去6个月内，主力净占比的参考阈值：")
print(f"  50% 分位: {result['50%']:.2f}%")
print(f"  75% 分位: {result['75%']:.2f}%")
print(f"  90% 分位: {result['90%']:.2f}%")

#%%
import pandas as pd
import numpy as np
from pyecharts.charts import Kline, Bar, Line, Scatter, Grid
from pyecharts import options as opts



def analyze_main_force_open_position():

    # ------------------------------------------------------------------------------
    # 1. 读取并预处理数据
    # --------------------------------------------------------------------」----------
    
    # 1.1 读取 K 线数据（跳过首行）
    # df_kline = pd.read_csv("/mnt/data/179c2498-8df2-4bc9-9dc2-d691b0ace48f.csv", skiprows=1)
    df_kline["日期"] = pd.to_datetime(df_kline["trade_date"], format="%Y%m%d")
    
    # 1.2 读取资金流数据
    # df_funds = pd.read_csv("/mnt/data/4cb92578-fefd-4882-aaf5-e8b89c1a115e.csv")
    df_funds["日期"] = pd.to_datetime(df_funds["日期"], format="%Y-%m-%d")
    
    # 1.3 合并
    df = pd.merge(df_kline, df_funds, on="日期", how="inner")
    

    # 1.4 确保数值列类型
    df["pct_chg"] = pd.to_numeric(df["pct_chg"], errors="coerce")
    df["vol"] = pd.to_numeric(df["vol"], errors="coerce")
    df["主力净流入-净占比"] = pd.to_numeric(df["主力净流入-净占比"], errors="coerce")
    
    # 1.5 计算全周期的 75% 分位数，用于“强势介入”判断
    threshold_75 = df["主力净流入-净占比"].quantile(0.75)
    
    # ------------------------------------------------------------------------------
    # 2. 标记主力入场信号（加入“下跌节奏放缓”辅助条件）
    # ------------------------------------------------------------------------------
    
    # 2.1 强势介入：主力净流入 > 0 且 主力净占比 > 75%分位
    df["强势介入"] = (df["主力净流入-净额"] > 0) & (df["主力净流入-净占比"] > threshold_75)
    
    # 2.2 异动不涨：在“强势介入”基础上，当日 |pct_chg| < 1%
    df["异动不涨"] = df["强势介入"] & (df["pct_chg"].abs() < 1.0)
    
    # 2.3 下跌节奏放缓：在“强势介入”基础上，pct_chg < 0 且 pct_chg > -2%
    threshold_drop = 2.0  # 下跌幅度小于 2% 认为下跌节奏放缓
    df["下跌放缓"] = df["强势介入"] & (df["pct_chg"] < 0) & (df["pct_chg"] > -threshold_drop)
    
    # 2.4 信号：只要“异动不涨” 或 “下跌放缓” 其中之一成立
    df["信号"] = ((df["异动不涨"]) | (df["下跌放缓"])).astype(int)
    
    # 2.5 连续 N 天出现信号
    N = 2
    df["连续信号数"] = df["信号"].rolling(window=N).sum().fillna(0).astype(int)
    
    # 2.6 最终“主力入场信号”：连续 N 天都满足“信号”条件
    df["主力入场信号"] = df["连续信号数"] == N

    df_sorted = df.sort_values(by='日期', ascending=True)
    return df_sorted



df = analyze_main_force_open_position()
df[df["主力入场信号"] == True]
#%%
import pandas as pd
import numpy as np
from pyecharts.charts import Kline, Bar, Line, Scatter, Grid
from pyecharts import options as opts



# ------------------------------------------------------------------------------
# 3. 准备绘图数据
# ------------------------------------------------------------------------------

# 3.1 K 线数据格式：[[open, close, low, high], ...]
kline_data = [
    [
        float(df["open"].iloc[i]),
        float(df["close"].iloc[i]),
        float(df["low"].iloc[i]),
        float(df["high"].iloc[i])
    ]
    for i in range(len(df))
]
date_list = df["日期"].dt.strftime("%Y-%m-%d").tolist()

# 3.2 成交量柱状
bar_data = df["vol"].tolist()

# 3.3 主力净占比折线
line_data = df["主力净流入-净占比"].tolist()

# 3.4 主力入场信号散点：若“主力入场信号”为 True，则取当日最低价，否则 None
scatter_data = [
    float(df["low"].iloc[i]) if df["主力入场信号"].iloc[i] else None
    for i in range(len(df))
]

# ------------------------------------------------------------------------------
# 4. 用 pyecharts 构造图表并导出 HTML
# ------------------------------------------------------------------------------

# 4.1 K 线图
kline = (
    Kline(init_opts=opts.InitOpts(width="1000px", height="500px"))
    .add_xaxis(date_list)
    .add_yaxis(
        "K 线",
        kline_data,
        itemstyle_opts=opts.ItemStyleOpts(
            color="#ef232a",     # 阳线颜色
            color0="#14b143",    # 阴线颜色
            border_color="#ef232a",
            border_color0="#14b143"
        ),
    )
    .set_global_opts(
        xaxis_opts=opts.AxisOpts(type_="category", boundary_gap=False, grid_index=0),
        yaxis_opts=opts.AxisOpts(
            is_scale=True,
            splitarea_opts=opts.SplitAreaOpts(
                is_show=True, areastyle_opts=opts.AreaStyleOpts(opacity=1)
            )
        ),
        title_opts=opts.TitleOpts(title=f"{code} 主力入场信号 K 线图"),
        legend_opts=opts.LegendOpts(is_show=False),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
        datazoom_opts=[
            opts.DataZoomOpts(is_show=True, type_="inside", xaxis_index=[0, 1], range_start=50, range_end=100),
            opts.DataZoomOpts(is_show=True, xaxis_index=[0, 1], range_start=50, range_end=100)
        ],
    )
)

# 4.2 成交量柱状:
bar = (
    Bar()
    .add_xaxis(date_list)
    .add_yaxis(
        "成交量",
        bar_data,
        yaxis_index=1,
        xaxis_index=0,
        itemstyle_opts=opts.ItemStyleOpts(color="#7f7f7f"),
        label_opts=opts.LabelOpts(is_show=False) 
    )
    .set_global_opts(
        xaxis_opts=opts.AxisOpts(is_show=False),
        yaxis_opts=opts.AxisOpts(
            is_scale=True,
            split_number=2,
            axislabel_opts=opts.LabelOpts(is_show=False)
        ),
        legend_opts=opts.LegendOpts(is_show=False),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
    )
)

# 4.3 主力净占比折线:
line = (
    Line()
    .add_xaxis(date_list)
    .add_yaxis(
        "主力净占比",
        line_data,
        yaxis_index=2,
        xaxis_index=0,
        linestyle_opts=opts.LineStyleOpts(color="#5470C6", width=2),
        label_opts=opts.LabelOpts(is_show=False)
    )
    .set_global_opts(
        xaxis_opts=opts.AxisOpts(is_show=False),
        yaxis_opts=opts.AxisOpts(
            is_scale=True,
            position="right",
            splitline_opts=opts.SplitLineOpts(is_show=False)
        ),
        legend_opts=opts.LegendOpts(is_show=False),
        tooltip_opts=opts.TooltipOpts(trigger="axis"),
    )
)

# 4.4 建仓信号散点 - 直接添加到K线图中:
scatter = (
    Scatter()
    .add_xaxis(date_list)
    .add_yaxis(
        "主力入场信号",
        scatter_data,
        symbol="triangle",
        symbol_size=12,
        itemstyle_opts=opts.ItemStyleOpts(color="purple")
    )
)

# 将散点图叠加到K线图上
kline = kline.overlap(scatter)

# 4.5 使用 Grid 布局，将三张图组合
grid = (
    Grid(init_opts=opts.InitOpts(width="100%", height="700px"))
    .add(
        kline,
        grid_index=0,
        grid_opts=opts.GridOpts(pos_left="10%", pos_right="8%", pos_top="5%", height="40%")
    )
    .add(
        bar,
        grid_index=1,
        grid_opts=opts.GridOpts(pos_left="10%", pos_right="8%", pos_top="50%", height="15%")
    )
    .add(
        line,
        grid_index=2,
        grid_opts=opts.GridOpts(pos_left="10%", pos_right="8%", pos_top="70%", height="15%")
    )
    .add(
        scatter,
        grid_index=0,
        grid_opts=opts.GridOpts(pos_left="10%", pos_right="8%", pos_top="5%", height="40%")
    )
)

# 4.6 导出成 HTML 文件
html_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_build_signal.html"
grid.render(html_path)

# 返回 HTML 文件路径
# html_path ​:contentReference[oaicite:0]{index=0}​

#%%

#%%

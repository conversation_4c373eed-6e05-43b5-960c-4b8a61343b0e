<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="70b48bbe2303471585c9a9029f0a209b" class="chart-container" style="width:1200px; height:800px; "></div>
    <script>
        var chart_70b48bbe2303471585c9a9029f0a209b = echarts.init(
            document.getElementById('70b48bbe2303471585c9a9029f0a209b'), 'white', {renderer: 'canvas'});
        var option_70b48bbe2303471585c9a9029f0a209b = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8d44\u91d1\u6d41\u5165",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0.14,
                0.98,
                1.65,
                4.27,
                29.59
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#ff4444"
            }
        },
        {
            "type": "bar",
            "name": "\u8d44\u91d1\u6d41\u51fa",
            "legendHoverLink": true,
            "data": [
                -297.71,
                -249.84,
                -243.41,
                -232.95,
                -221.98,
                -182.77,
                -123.94,
                -117.7,
                -94.93,
                -82.11,
                -82.08,
                -80.01,
                -78.46,
                -77.84,
                -70.17,
                -68.42,
                -67.83,
                -62.55,
                -62.27,
                -61.86,
                -59.07,
                -57.93,
                -54.46,
                -50.92,
                -47.96,
                -47.26,
                -46.98,
                -44.04,
                -42.79,
                -42.06,
                -39.04,
                -38.48,
                -36.4,
                -35.09,
                -34.48,
                -33.8,
                -30.8,
                -29.75,
                -29.6,
                -29.38,
                -28.28,
                -27.56,
                -27.01,
                -26.66,
                -26.0,
                -24.77,
                -24.13,
                -21.62,
                -21.34,
                -18.97,
                -17.34,
                -16.95,
                -15.81,
                -15.0,
                -14.49,
                -14.47,
                -14.44,
                -14.27,
                -12.61,
                -11.91,
                -11.77,
                -11.21,
                -10.59,
                -10.43,
                -9.71,
                -9.65,
                -9.28,
                -6.2,
                -5.74,
                -5.65,
                -5.49,
                -5.29,
                -4.87,
                -4.27,
                -3.02,
                -2.87,
                -2.0,
                -1.85,
                -1.13,
                -0.93,
                -0.02,
                0,
                0,
                0,
                0,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#00aa44"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8d44\u91d1\u6d41\u5165",
                "\u8d44\u91d1\u6d41\u51fa"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "shadow"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "name": "\u8d44\u91d1\u6d41\u5165 (\u4ebf\u5143)",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "yAxis": [
        {
            "name": "\u884c\u4e1a",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u6c7d\u8f66\u96f6\u90e8\u4ef6",
                "\u534a\u5bfc\u4f53",
                "\u8f6f\u4ef6\u5f00\u53d1",
                "\u901a\u7528\u8bbe\u5907",
                "\u4e13\u7528\u8bbe\u5907",
                "\u4e92\u8054\u7f51\u670d\u52a1",
                "\u917f\u9152\u884c\u4e1a",
                "\u901a\u4fe1\u8bbe\u5907",
                "\u7535\u529b\u884c\u4e1a",
                "\u6d88\u8d39\u7535\u5b50",
                "\u5316\u5b66\u5236\u54c1",
                "\u5149\u5b66\u5149\u7535\u5b50",
                "\u519c\u7267\u9972\u6e14",
                "\u822a\u5929\u822a\u7a7a",
                "\u7535\u5b50\u5143\u4ef6",
                "\u5316\u5b66\u5236\u836f",
                "\u5149\u4f0f\u8bbe\u5907",
                "\u7535\u7f51\u8bbe\u5907",
                "\u623f\u5730\u4ea7\u5f00\u53d1",
                "\u7535\u673a",
                "\u5316\u5b66\u539f\u6599",
                "\u5c0f\u91d1\u5c5e",
                "\u8bc1\u5238",
                "\u94f6\u884c",
                "\u73af\u4fdd\u884c\u4e1a",
                "\u5546\u4e1a\u767e\u8d27",
                "\u822a\u8fd0\u6e2f\u53e3",
                "\u5851\u6599\u5236\u54c1",
                "\u4eea\u5668\u4eea\u8868",
                "\u4e2d\u836f",
                "\u7eba\u7ec7\u670d\u88c5",
                "\u98df\u54c1\u996e\u6599",
                "\u6c7d\u8f66\u6574\u8f66",
                "\u8ba1\u7b97\u673a\u8bbe\u5907",
                "\u5de5\u7a0b\u673a\u68b0",
                "\u8239\u8236\u5236\u9020",
                "\u5305\u88c5\u6750\u6599",
                "\u5316\u80a5\u884c\u4e1a",
                "\u901a\u4fe1\u670d\u52a1",
                "\u7efc\u5408\u884c\u4e1a",
                "\u7535\u6e90\u8bbe\u5907",
                "\u7269\u6d41\u884c\u4e1a",
                "\u533b\u7597\u5668\u68b0",
                "\u519c\u836f\u517d\u836f",
                "\u6e38\u620f",
                "\u94a2\u94c1\u884c\u4e1a",
                "\u6709\u8272\u91d1\u5c5e",
                "\u5bb6\u7528\u8f7b\u5de5",
                "\u65c5\u6e38\u9152\u5e97",
                "\u7f8e\u5bb9\u62a4\u7406",
                "\u5de5\u7a0b\u5efa\u8bbe",
                "\u80fd\u6e90\u91d1\u5c5e",
                "\u9020\u7eb8\u5370\u5237",
                "\u5bb6\u7535\u884c\u4e1a",
                "\u71c3\u6c14",
                "\u4e13\u4e1a\u670d\u52a1",
                "\u5316\u7ea4\u884c\u4e1a",
                "\u88c5\u4fee\u88c5\u9970",
                "\u4ea4\u8fd0\u8bbe\u5907",
                "\u98ce\u7535\u8bbe\u5907",
                "\u533b\u836f\u5546\u4e1a",
                "\u516c\u7528\u4e8b\u4e1a",
                "\u751f\u7269\u5236\u54c1",
                "\u623f\u5730\u4ea7\u670d\u52a1",
                "\u6587\u5316\u4f20\u5a92",
                "\u73bb\u7483\u73bb\u7ea4",
                "\u77f3\u6cb9\u884c\u4e1a",
                "\u91c7\u6398\u884c\u4e1a",
                "\u591a\u5143\u91d1\u878d",
                "\u7535\u5b50\u5316\u5b66\u54c1",
                "\u822a\u7a7a\u673a\u573a",
                "\u6c34\u6ce5\u5efa\u6750",
                "\u7535\u6c60",
                "\u88c5\u4fee\u5efa\u6750",
                "\u6a61\u80f6\u5236\u54c1",
                "\u6c7d\u8f66\u670d\u52a1",
                "\u975e\u91d1\u5c5e\u6750\u6599",
                "\u6559\u80b2",
                "\u8d35\u91d1\u5c5e",
                "\u5de5\u7a0b\u54a8\u8be2\u670d\u52a1",
                "\u7164\u70ad\u884c\u4e1a",
                "\u94c1\u8def\u516c\u8def",
                "\u533b\u7597\u670d\u52a1",
                "\u73e0\u5b9d\u9996\u9970",
                "\u8d38\u6613\u884c\u4e1a",
                "\u4fdd\u9669"
            ]
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u8fd125\u65e5\u884c\u4e1a\u4e3b\u529b\u8d44\u91d1\u6d41\u5165 (\u4ebf\u5143)",
            "target": "blank",
            "subtext": "\u7ea2\u8272=\u6d41\u5165\uff0c\u7eff\u8272=\u6d41\u51fa",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_70b48bbe2303471585c9a9029f0a209b.setOption(option_70b48bbe2303471585c9a9029f0a209b);
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="12267e7f95b6401c9b94160fb716d5d0" class="chart-container" style="width:1400px; height:1000px; "></div>
    <script>
        var chart_12267e7f95b6401c9b94160fb716d5d0 = echarts.init(
            document.getElementById('12267e7f95b6401c9b94160fb716d5d0'), 'white', {renderer: 'canvas'});
        var option_12267e7f95b6401c9b94160fb716d5d0 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "line",
            "name": "\u6587\u5316\u4f20\u5a92",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "06-05",
                    15.09
                ],
                [
                    "06-06",
                    -11.91
                ],
                [
                    "06-09",
                    32.17
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff6b6b"
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "color": "#ff6b6b"
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u901a\u4fe1\u8bbe\u5907",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "06-05",
                    42.53
                ],
                [
                    "06-06",
                    -7.46
                ],
                [
                    "06-09",
                    -0.8
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#4ecdc4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "color": "#4ecdc4"
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u8d38\u6613\u884c\u4e1a",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "06-05",
                    -3.56
                ],
                [
                    "06-06",
                    -1.69
                ],
                [
                    "06-09",
                    32.46
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#45b7d1"
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "color": "#45b7d1"
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u4e92\u8054\u7f51\u670d\u52a1",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "06-05",
                    43.18
                ],
                [
                    "06-06",
                    -32.14
                ],
                [
                    "06-09",
                    10.94
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#96ceb4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "color": "#96ceb4"
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u8bc1\u5238",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "06-05",
                    10.79
                ],
                [
                    "06-06",
                    -14.95
                ],
                [
                    "06-09",
                    20.9
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#feca57"
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "color": "#feca57"
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u7535\u7f51\u8bbe\u5907",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "06-05",
                    10.07
                ],
                [
                    "06-06",
                    8.62
                ],
                [
                    "06-09",
                    -2.85
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff9ff3"
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "color": "#ff9ff3"
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6d88\u8d39\u7535\u5b50",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "06-05",
                    30.68
                ],
                [
                    "06-06",
                    -20.88
                ],
                [
                    "06-09",
                    5.6
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#54a0ff"
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "color": "#54a0ff"
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u534a\u5bfc\u4f53",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "06-05",
                    32.22
                ],
                [
                    "06-06",
                    5.12
                ],
                [
                    "06-09",
                    -22.32
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#5f27cd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "color": "#5f27cd"
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u7535\u6e90\u8bbe\u5907",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "06-05",
                    10.89
                ],
                [
                    "06-06",
                    -3.88
                ],
                [
                    "06-09",
                    1.74
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#00d2d3"
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "color": "#00d2d3"
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u5c0f\u91d1\u5c5e",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "06-05",
                    -18.47
                ],
                [
                    "06-06",
                    -4.59
                ],
                [
                    "06-09",
                    31.57
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff9f43"
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "color": "#ff9f43"
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u6587\u5316\u4f20\u5a92",
                "\u901a\u4fe1\u8bbe\u5907",
                "\u8d38\u6613\u884c\u4e1a",
                "\u4e92\u8054\u7f51\u670d\u52a1",
                "\u8bc1\u5238",
                "\u7535\u7f51\u8bbe\u5907",
                "\u6d88\u8d39\u7535\u5b50",
                "\u534a\u5bfc\u4f53",
                "\u7535\u6e90\u8bbe\u5907",
                "\u5c0f\u91d1\u5c5e"
            ],
            "selected": {},
            "type": "scroll",
            "show": true,
            "right": "2%",
            "top": "10%",
            "orient": "vertical",
            "padding": 5,
            "itemGap": 3,
            "itemWidth": 15,
            "itemHeight": 12,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "position": "top",
        "textStyle": {
            "fontSize": 14
        },
        "backgroundColor": "rgba(255,255,255,0.9)",
        "borderColor": "#ccc",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "06-05",
                "06-06",
                "06-09"
            ]
        }
    ],
    "yAxis": [
        {
            "name": "\u4e3b\u529b\u8d44\u91d1\u6d41\u5165 (\u4ebf\u5143)",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u8fd15\u65e5\u4e3b\u529b\u8d44\u91d1\u6d41\u5165\u524d10\u884c\u4e1a\u8d8b\u52bf",
            "target": "blank",
            "subtext": "\u5355\u4f4d\uff1a\u4ebf\u5143",
            "subtarget": "blank",
            "top": "300px",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "top": "350px",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_12267e7f95b6401c9b94160fb716d5d0.setOption(option_12267e7f95b6401c9b94160fb716d5d0);
    </script>
</body>
</html>

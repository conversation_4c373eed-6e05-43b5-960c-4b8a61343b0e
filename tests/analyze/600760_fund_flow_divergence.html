<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="2d819b9d37cc4d0b9b9ab835caf3118d" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_2d819b9d37cc4d0b9b9ab835caf3118d = echarts.init(
            document.getElementById('2d819b9d37cc4d0b9b9ab835caf3118d'), 'white', {renderer: 'canvas'});
            
    // 等待页面加载完成
    setTimeout(function() {
        // 获取所有图表实例
        var charts = [];
        var chartDoms = document.querySelectorAll('[_echarts_instance_]');
        chartDoms.forEach(function(dom) {
            var chart = echarts.getInstanceByDom(dom);
            if (chart) {
                charts.push(chart);
            }
        });

        console.log('找到图表数量:', charts.length);

        // 设置缩放联动
        if (charts.length >= 2) {
            echarts.connect(charts);
            console.log('图表缩放联动已设置');
        }
    }, 100);

        var option_2d819b9d37cc4d0b9b9ab835caf3118d = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    49.4,
                    49.8,
                    49.21,
                    50.1
                ],
                [
                    49.7,
                    48.55,
                    48.09,
                    49.76
                ],
                [
                    48.79,
                    47.71,
                    47.6,
                    48.79
                ],
                [
                    47.72,
                    48.14,
                    47.48,
                    48.48
                ],
                [
                    48.2,
                    48.47,
                    47.5,
                    48.67
                ],
                [
                    48.27,
                    47.65,
                    47.08,
                    48.28
                ],
                [
                    48.69,
                    48.14,
                    48.0,
                    49.36
                ],
                [
                    47.98,
                    48.49,
                    47.76,
                    48.7
                ],
                [
                    48.99,
                    50.51,
                    48.99,
                    50.72
                ],
                [
                    49.98,
                    48.59,
                    48.51,
                    50.05
                ],
                [
                    48.6,
                    49.44,
                    48.35,
                    49.75
                ],
                [
                    49.57,
                    49.35,
                    49.2,
                    49.9
                ],
                [
                    49.4,
                    50.0,
                    49.4,
                    50.69
                ],
                [
                    49.5,
                    49.64,
                    48.85,
                    49.85
                ],
                [
                    49.48,
                    50.09,
                    49.33,
                    50.28
                ],
                [
                    49.96,
                    48.49,
                    48.31,
                    49.96
                ],
                [
                    48.25,
                    48.58,
                    48.12,
                    48.69
                ],
                [
                    48.56,
                    48.17,
                    47.78,
                    48.99
                ],
                [
                    49.0,
                    49.6,
                    49.0,
                    50.67
                ],
                [
                    52.01,
                    50.46,
                    50.22,
                    52.5
                ],
                [
                    50.59,
                    50.1,
                    49.6,
                    51.4
                ],
                [
                    49.9,
                    50.72,
                    49.6,
                    51.31
                ],
                [
                    50.17,
                    48.32,
                    47.88,
                    50.17
                ],
                [
                    48.03,
                    46.34,
                    46.21,
                    48.33
                ],
                [
                    46.34,
                    45.75,
                    44.91,
                    46.67
                ],
                [
                    45.64,
                    46.4,
                    45.51,
                    46.5
                ],
                [
                    46.2,
                    45.06,
                    43.6,
                    46.25
                ],
                [
                    44.69,
                    47.49,
                    44.51,
                    49.49
                ],
                [
                    46.85,
                    47.15,
                    46.85,
                    48.48
                ],
                [
                    46.5,
                    46.93,
                    46.07,
                    47.86
                ],
                [
                    46.95,
                    48.28,
                    46.88,
                    48.38
                ],
                [
                    48.29,
                    47.5,
                    47.5,
                    48.43
                ],
                [
                    47.59,
                    47.16,
                    46.99,
                    47.99
                ],
                [
                    47.0,
                    47.98,
                    46.9,
                    48.19
                ],
                [
                    48.0,
                    47.71,
                    47.4,
                    48.09
                ],
                [
                    47.76,
                    47.0,
                    46.77,
                    47.87
                ],
                [
                    46.9,
                    46.9,
                    45.59,
                    47.0
                ],
                [
                    47.26,
                    47.17,
                    46.8,
                    47.45
                ],
                [
                    46.83,
                    46.93,
                    46.68,
                    47.24
                ],
                [
                    47.2,
                    46.56,
                    46.35,
                    47.2
                ],
                [
                    46.8,
                    46.59,
                    45.8,
                    46.97
                ],
                [
                    46.55,
                    47.95,
                    46.12,
                    48.48
                ],
                [
                    47.65,
                    47.94,
                    47.44,
                    48.35
                ],
                [
                    47.94,
                    48.18,
                    47.6,
                    48.28
                ],
                [
                    48.01,
                    47.2,
                    47.11,
                    48.01
                ],
                [
                    47.03,
                    47.22,
                    46.57,
                    47.27
                ],
                [
                    47.22,
                    47.22,
                    47.06,
                    48.02
                ],
                [
                    47.0,
                    46.31,
                    46.14,
                    47.2
                ],
                [
                    46.32,
                    45.4,
                    45.1,
                    46.5
                ],
                [
                    45.4,
                    44.62,
                    44.25,
                    45.46
                ],
                [
                    44.61,
                    44.92,
                    44.39,
                    44.98
                ],
                [
                    44.88,
                    45.41,
                    44.43,
                    45.69
                ],
                [
                    45.3,
                    45.9,
                    45.0,
                    46.23
                ],
                [
                    45.86,
                    45.36,
                    45.13,
                    45.98
                ],
                [
                    45.08,
                    44.7,
                    44.25,
                    45.28
                ],
                [
                    44.44,
                    44.25,
                    43.93,
                    44.67
                ],
                [
                    44.3,
                    44.05,
                    43.51,
                    44.39
                ],
                [
                    44.05,
                    43.54,
                    43.49,
                    44.36
                ],
                [
                    43.58,
                    43.58,
                    43.33,
                    44.07
                ],
                [
                    43.8,
                    44.88,
                    43.6,
                    45.1
                ],
                [
                    44.68,
                    44.29,
                    44.11,
                    45.09
                ],
                [
                    44.48,
                    44.36,
                    44.0,
                    44.54
                ],
                [
                    44.25,
                    45.32,
                    44.01,
                    46.32
                ],
                [
                    45.59,
                    45.17,
                    45.01,
                    46.29
                ],
                [
                    44.88,
                    46.31,
                    44.69,
                    46.61
                ],
                [
                    46.31,
                    45.45,
                    45.39,
                    46.98
                ],
                [
                    45.48,
                    45.08,
                    44.77,
                    45.66
                ],
                [
                    45.08,
                    45.17,
                    44.59,
                    45.44
                ],
                [
                    45.17,
                    45.24,
                    44.97,
                    46.07
                ],
                [
                    45.26,
                    44.75,
                    44.7,
                    45.35
                ],
                [
                    44.76,
                    44.19,
                    44.08,
                    44.79
                ],
                [
                    44.1,
                    44.45,
                    44.08,
                    45.1
                ],
                [
                    44.02,
                    43.97,
                    43.68,
                    44.49
                ],
                [
                    44.03,
                    43.85,
                    43.35,
                    44.25
                ],
                [
                    43.83,
                    43.77,
                    43.6,
                    44.18
                ],
                [
                    43.78,
                    43.93,
                    43.56,
                    44.02
                ],
                [
                    43.93,
                    43.6,
                    43.34,
                    43.99
                ],
                [
                    43.6,
                    43.42,
                    43.03,
                    43.67
                ],
                [
                    42.91,
                    42.28,
                    42.26,
                    43.13
                ],
                [
                    43.68,
                    44.49,
                    43.68,
                    45.13
                ],
                [
                    44.49,
                    42.9,
                    42.73,
                    44.49
                ],
                [
                    42.68,
                    42.94,
                    42.53,
                    43.16
                ],
                [
                    41.68,
                    41.24,
                    39.06,
                    42.63
                ],
                [
                    41.22,
                    42.0,
                    41.13,
                    42.0
                ],
                [
                    41.91,
                    44.27,
                    41.66,
                    44.81
                ],
                [
                    44.26,
                    44.6,
                    43.57,
                    45.1
                ],
                [
                    44.6,
                    44.83,
                    44.03,
                    44.83
                ],
                [
                    44.83,
                    44.73,
                    44.16,
                    44.95
                ],
                [
                    44.5,
                    44.0,
                    43.8,
                    44.51
                ],
                [
                    44.7,
                    44.48,
                    43.58,
                    44.75
                ],
                [
                    44.1,
                    44.2,
                    43.96,
                    44.6
                ],
                [
                    43.93,
                    44.04,
                    43.31,
                    44.18
                ],
                [
                    43.83,
                    44.2,
                    43.74,
                    44.4
                ],
                [
                    44.0,
                    43.88,
                    43.73,
                    44.22
                ],
                [
                    43.88,
                    43.05,
                    43.01,
                    43.97
                ],
                [
                    42.99,
                    42.68,
                    42.67,
                    43.07
                ],
                [
                    42.71,
                    42.84,
                    42.31,
                    42.93
                ],
                [
                    42.8,
                    42.48,
                    42.4,
                    42.83
                ],
                [
                    41.5,
                    42.67,
                    41.28,
                    43.0
                ],
                [
                    42.43,
                    42.66,
                    42.4,
                    42.78
                ],
                [
                    42.98,
                    43.22,
                    42.9,
                    43.5
                ],
                [
                    44.4,
                    46.01,
                    44.35,
                    47.47
                ],
                [
                    46.15,
                    48.66,
                    45.13,
                    49.66
                ],
                [
                    48.49,
                    46.18,
                    46.03,
                    48.5
                ],
                [
                    46.58,
                    50.07,
                    46.25,
                    50.7
                ],
                [
                    49.5,
                    47.81,
                    47.54,
                    49.5
                ],
                [
                    47.82,
                    47.38,
                    46.89,
                    48.19
                ],
                [
                    47.09,
                    46.92,
                    46.69,
                    47.75
                ],
                [
                    46.93,
                    47.2,
                    46.83,
                    48.05
                ],
                [
                    47.76,
                    47.69,
                    47.47,
                    48.96
                ],
                [
                    47.29,
                    47.68,
                    46.4,
                    48.2
                ],
                [
                    47.16,
                    47.99,
                    46.89,
                    48.29
                ],
                [
                    48.0,
                    48.13,
                    47.74,
                    49.86
                ],
                [
                    47.8,
                    48.01,
                    47.8,
                    48.85
                ],
                [
                    48.21,
                    48.33,
                    47.55,
                    48.94
                ],
                [
                    48.03,
                    47.65,
                    47.11,
                    48.59
                ],
                [
                    47.51,
                    47.25,
                    47.17,
                    48.18
                ],
                [
                    47.13,
                    47.58,
                    46.93,
                    47.83
                ],
                [
                    47.35,
                    48.03,
                    46.95,
                    48.66
                ],
                [
                    48.5,
                    49.38,
                    48.25,
                    50.0
                ],
                [
                    49.4,
                    49.36,
                    48.8,
                    49.43
                ],
                [
                    49.38,
                    49.6,
                    48.7,
                    49.93
                ],
                [
                    50.5,
                    50.0,
                    49.97,
                    51.5
                ],
                [
                    53.0,
                    54.65,
                    52.57,
                    55.0
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    1.12
                ],
                [
                    "2024-12-06",
                    1.74
                ],
                [
                    "2024-12-09",
                    -21.92
                ],
                [
                    "2024-12-10",
                    0.76
                ],
                [
                    "2024-12-11",
                    -1.6
                ],
                [
                    "2024-12-12",
                    2.41
                ],
                [
                    "2024-12-13",
                    -12.09
                ],
                [
                    "2024-12-16",
                    4.48
                ],
                [
                    "2024-12-17",
                    1.55
                ],
                [
                    "2024-12-18",
                    0.16
                ],
                [
                    "2024-12-19",
                    -5.82
                ],
                [
                    "2024-12-20",
                    -0.73
                ],
                [
                    "2024-12-23",
                    -14.49
                ],
                [
                    "2024-12-24",
                    4.11
                ],
                [
                    "2024-12-25",
                    -8.95
                ],
                [
                    "2024-12-26",
                    5.81
                ],
                [
                    "2024-12-27",
                    -6.03
                ],
                [
                    "2024-12-30",
                    -7.0
                ],
                [
                    "2024-12-31",
                    -2.54
                ],
                [
                    "2025-01-02",
                    -14.65
                ],
                [
                    "2025-01-03",
                    -4.46
                ],
                [
                    "2025-01-06",
                    -8.47
                ],
                [
                    "2025-01-07",
                    7.09
                ],
                [
                    "2025-01-08",
                    -8.74
                ],
                [
                    "2025-01-09",
                    6.03
                ],
                [
                    "2025-01-10",
                    -4.08
                ],
                [
                    "2025-01-13",
                    -3.61
                ],
                [
                    "2025-01-14",
                    4.68
                ],
                [
                    "2025-01-15",
                    -3.4
                ],
                [
                    "2025-01-16",
                    -12.89
                ],
                [
                    "2025-01-17",
                    7.74
                ],
                [
                    "2025-01-20",
                    2.53
                ],
                [
                    "2025-01-21",
                    -8.23
                ],
                [
                    "2025-01-22",
                    -8.45
                ],
                [
                    "2025-01-23",
                    -2.6
                ],
                [
                    "2025-01-24",
                    -9.77
                ],
                [
                    "2025-01-27",
                    -7.7
                ],
                [
                    "2025-02-05",
                    4.69
                ],
                [
                    "2025-02-06",
                    3.59
                ],
                [
                    "2025-02-07",
                    -2.77
                ],
                [
                    "2025-02-10",
                    -12.74
                ],
                [
                    "2025-02-11",
                    -17.1
                ],
                [
                    "2025-02-12",
                    -18.03
                ],
                [
                    "2025-02-13",
                    -1.72
                ],
                [
                    "2025-02-14",
                    -20.88
                ],
                [
                    "2025-02-17",
                    -17.33
                ],
                [
                    "2025-02-18",
                    -19.33
                ],
                [
                    "2025-02-19",
                    -8.82
                ],
                [
                    "2025-02-20",
                    6.09
                ],
                [
                    "2025-02-21",
                    -7.33
                ],
                [
                    "2025-02-24",
                    -3.58
                ],
                [
                    "2025-02-25",
                    -17.9
                ],
                [
                    "2025-02-26",
                    -12.4
                ],
                [
                    "2025-02-27",
                    -4.07
                ],
                [
                    "2025-02-28",
                    -0.77
                ],
                [
                    "2025-03-03",
                    -5.11
                ],
                [
                    "2025-03-04",
                    8.99
                ],
                [
                    "2025-03-05",
                    -12.74
                ],
                [
                    "2025-03-06",
                    -10.83
                ],
                [
                    "2025-03-07",
                    2.24
                ],
                [
                    "2025-03-10",
                    -7.08
                ],
                [
                    "2025-03-11",
                    13.7
                ],
                [
                    "2025-03-12",
                    -4.16
                ],
                [
                    "2025-03-13",
                    -9.52
                ],
                [
                    "2025-03-14",
                    -2.08
                ],
                [
                    "2025-03-17",
                    2.12
                ],
                [
                    "2025-03-18",
                    -16.26
                ],
                [
                    "2025-03-19",
                    -24.16
                ],
                [
                    "2025-03-20",
                    7.87
                ],
                [
                    "2025-03-21",
                    3.45
                ],
                [
                    "2025-03-24",
                    -1.94
                ],
                [
                    "2025-03-25",
                    -7.03
                ],
                [
                    "2025-03-26",
                    3.81
                ],
                [
                    "2025-03-27",
                    -5.15
                ],
                [
                    "2025-03-28",
                    -6.43
                ],
                [
                    "2025-03-31",
                    -7.85
                ],
                [
                    "2025-04-01",
                    -4.88
                ],
                [
                    "2025-04-02",
                    5.97
                ],
                [
                    "2025-04-03",
                    -4.03
                ],
                [
                    "2025-04-07",
                    -19.95
                ],
                [
                    "2025-04-08",
                    -7.99
                ],
                [
                    "2025-04-09",
                    9.31
                ],
                [
                    "2025-04-10",
                    -0.36
                ],
                [
                    "2025-04-11",
                    -4.8
                ],
                [
                    "2025-04-14",
                    1.06
                ],
                [
                    "2025-04-15",
                    -12.64
                ],
                [
                    "2025-04-16",
                    -4.08
                ],
                [
                    "2025-04-17",
                    1.68
                ],
                [
                    "2025-04-18",
                    -10.98
                ],
                [
                    "2025-04-21",
                    -0.85
                ],
                [
                    "2025-04-22",
                    -9.76
                ],
                [
                    "2025-04-23",
                    -15.01
                ],
                [
                    "2025-04-24",
                    -8.41
                ],
                [
                    "2025-04-25",
                    7.12
                ],
                [
                    "2025-04-28",
                    -2.92
                ],
                [
                    "2025-04-29",
                    -0.16
                ],
                [
                    "2025-04-30",
                    0.63
                ],
                [
                    "2025-05-06",
                    -5.1
                ],
                [
                    "2025-05-07",
                    7.27
                ],
                [
                    "2025-05-08",
                    3.98
                ],
                [
                    "2025-05-09",
                    -10.38
                ],
                [
                    "2025-05-12",
                    8.62
                ],
                [
                    "2025-05-13",
                    -8.96
                ],
                [
                    "2025-05-14",
                    -1.84
                ],
                [
                    "2025-05-15",
                    -5.48
                ],
                [
                    "2025-05-16",
                    2.17
                ],
                [
                    "2025-05-19",
                    2.24
                ],
                [
                    "2025-05-20",
                    -2.85
                ],
                [
                    "2025-05-21",
                    6.54
                ],
                [
                    "2025-05-22",
                    3.56
                ],
                [
                    "2025-05-23",
                    -6.92
                ],
                [
                    "2025-05-26",
                    -4.38
                ],
                [
                    "2025-05-27",
                    -10.4
                ],
                [
                    "2025-05-28",
                    -3.52
                ],
                [
                    "2025-05-29",
                    0.18
                ],
                [
                    "2025-05-30",
                    0.9
                ],
                [
                    "2025-06-03",
                    2.35
                ],
                [
                    "2025-06-04",
                    2.77
                ],
                [
                    "2025-06-05",
                    5.12
                ],
                [
                    "2025-06-06",
                    -5.75
                ],
                [
                    "2025-06-09",
                    10.8
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    -2.17
                ],
                [
                    "2024-12-06",
                    -4.62
                ],
                [
                    "2024-12-09",
                    8.61
                ],
                [
                    "2024-12-10",
                    -7.92
                ],
                [
                    "2024-12-11",
                    0.71
                ],
                [
                    "2024-12-12",
                    -1.94
                ],
                [
                    "2024-12-13",
                    4.81
                ],
                [
                    "2024-12-16",
                    -3.54
                ],
                [
                    "2024-12-17",
                    -3.31
                ],
                [
                    "2024-12-18",
                    -3.01
                ],
                [
                    "2024-12-19",
                    -3.91
                ],
                [
                    "2024-12-20",
                    -2.66
                ],
                [
                    "2024-12-23",
                    -1.9
                ],
                [
                    "2024-12-24",
                    -12.0
                ],
                [
                    "2024-12-25",
                    0.12
                ],
                [
                    "2024-12-26",
                    -1.85
                ],
                [
                    "2024-12-27",
                    2.49
                ],
                [
                    "2024-12-30",
                    5.95
                ],
                [
                    "2024-12-31",
                    3.5
                ],
                [
                    "2025-01-02",
                    5.48
                ],
                [
                    "2025-01-03",
                    2.5
                ],
                [
                    "2025-01-06",
                    3.49
                ],
                [
                    "2025-01-07",
                    -2.77
                ],
                [
                    "2025-01-08",
                    9.71
                ],
                [
                    "2025-01-09",
                    -1.93
                ],
                [
                    "2025-01-10",
                    1.45
                ],
                [
                    "2025-01-13",
                    0.12
                ],
                [
                    "2025-01-14",
                    -1.58
                ],
                [
                    "2025-01-15",
                    6.36
                ],
                [
                    "2025-01-16",
                    5.26
                ],
                [
                    "2025-01-17",
                    -4.47
                ],
                [
                    "2025-01-20",
                    -2.57
                ],
                [
                    "2025-01-21",
                    1.42
                ],
                [
                    "2025-01-22",
                    -1.0
                ],
                [
                    "2025-01-23",
                    -1.44
                ],
                [
                    "2025-01-24",
                    -1.18
                ],
                [
                    "2025-01-27",
                    4.83
                ],
                [
                    "2025-02-05",
                    -2.25
                ],
                [
                    "2025-02-06",
                    -7.47
                ],
                [
                    "2025-02-07",
                    2.65
                ],
                [
                    "2025-02-10",
                    0.72
                ],
                [
                    "2025-02-11",
                    7.31
                ],
                [
                    "2025-02-12",
                    5.07
                ],
                [
                    "2025-02-13",
                    -0.27
                ],
                [
                    "2025-02-14",
                    7.6
                ],
                [
                    "2025-02-17",
                    8.4
                ],
                [
                    "2025-02-18",
                    6.52
                ],
                [
                    "2025-02-19",
                    1.7
                ],
                [
                    "2025-02-20",
                    -7.25
                ],
                [
                    "2025-02-21",
                    2.65
                ],
                [
                    "2025-02-24",
                    2.9
                ],
                [
                    "2025-02-25",
                    4.98
                ],
                [
                    "2025-02-26",
                    1.17
                ],
                [
                    "2025-02-27",
                    2.98
                ],
                [
                    "2025-02-28",
                    4.41
                ],
                [
                    "2025-03-03",
                    1.95
                ],
                [
                    "2025-03-04",
                    -4.16
                ],
                [
                    "2025-03-05",
                    4.8
                ],
                [
                    "2025-03-06",
                    0.13
                ],
                [
                    "2025-03-07",
                    -0.84
                ],
                [
                    "2025-03-10",
                    3.06
                ],
                [
                    "2025-03-11",
                    -6.35
                ],
                [
                    "2025-03-12",
                    5.27
                ],
                [
                    "2025-03-13",
                    5.33
                ],
                [
                    "2025-03-14",
                    2.73
                ],
                [
                    "2025-03-17",
                    3.91
                ],
                [
                    "2025-03-18",
                    8.09
                ],
                [
                    "2025-03-19",
                    8.14
                ],
                [
                    "2025-03-20",
                    -5.9
                ],
                [
                    "2025-03-21",
                    -1.9
                ],
                [
                    "2025-03-24",
                    6.79
                ],
                [
                    "2025-03-25",
                    4.86
                ],
                [
                    "2025-03-26",
                    -2.98
                ],
                [
                    "2025-03-27",
                    11.29
                ],
                [
                    "2025-03-28",
                    7.9
                ],
                [
                    "2025-03-31",
                    12.58
                ],
                [
                    "2025-04-01",
                    -6.94
                ],
                [
                    "2025-04-02",
                    3.95
                ],
                [
                    "2025-04-03",
                    6.25
                ],
                [
                    "2025-04-07",
                    1.5
                ],
                [
                    "2025-04-08",
                    -4.46
                ],
                [
                    "2025-04-09",
                    -2.0
                ],
                [
                    "2025-04-10",
                    -2.11
                ],
                [
                    "2025-04-11",
                    -0.2
                ],
                [
                    "2025-04-14",
                    1.42
                ],
                [
                    "2025-04-15",
                    5.52
                ],
                [
                    "2025-04-16",
                    -1.61
                ],
                [
                    "2025-04-17",
                    -1.35
                ],
                [
                    "2025-04-18",
                    1.49
                ],
                [
                    "2025-04-21",
                    -3.25
                ],
                [
                    "2025-04-22",
                    -1.79
                ],
                [
                    "2025-04-23",
                    2.42
                ],
                [
                    "2025-04-24",
                    -6.02
                ],
                [
                    "2025-04-25",
                    -0.34
                ],
                [
                    "2025-04-28",
                    3.66
                ],
                [
                    "2025-04-29",
                    -0.43
                ],
                [
                    "2025-04-30",
                    -3.45
                ],
                [
                    "2025-05-06",
                    2.74
                ],
                [
                    "2025-05-07",
                    -6.22
                ],
                [
                    "2025-05-08",
                    -0.98
                ],
                [
                    "2025-05-09",
                    4.56
                ],
                [
                    "2025-05-12",
                    -6.4
                ],
                [
                    "2025-05-13",
                    5.71
                ],
                [
                    "2025-05-14",
                    0.29
                ],
                [
                    "2025-05-15",
                    0.39
                ],
                [
                    "2025-05-16",
                    -1.11
                ],
                [
                    "2025-05-19",
                    -0.88
                ],
                [
                    "2025-05-20",
                    -3.8
                ],
                [
                    "2025-05-21",
                    -0.42
                ],
                [
                    "2025-05-22",
                    -0.32
                ],
                [
                    "2025-05-23",
                    1.66
                ],
                [
                    "2025-05-26",
                    3.98
                ],
                [
                    "2025-05-27",
                    3.2
                ],
                [
                    "2025-05-28",
                    -3.46
                ],
                [
                    "2025-05-29",
                    -3.5
                ],
                [
                    "2025-05-30",
                    0.32
                ],
                [
                    "2025-06-03",
                    -1.62
                ],
                [
                    "2025-06-04",
                    1.19
                ],
                [
                    "2025-06-05",
                    -0.28
                ],
                [
                    "2025-06-06",
                    5.26
                ],
                [
                    "2025-06-09",
                    -9.89
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    1.05
                ],
                [
                    "2024-12-06",
                    2.88
                ],
                [
                    "2024-12-09",
                    13.32
                ],
                [
                    "2024-12-10",
                    7.16
                ],
                [
                    "2024-12-11",
                    0.89
                ],
                [
                    "2024-12-12",
                    -0.48
                ],
                [
                    "2024-12-13",
                    7.28
                ],
                [
                    "2024-12-16",
                    -0.94
                ],
                [
                    "2024-12-17",
                    1.76
                ],
                [
                    "2024-12-18",
                    2.86
                ],
                [
                    "2024-12-19",
                    9.73
                ],
                [
                    "2024-12-20",
                    3.4
                ],
                [
                    "2024-12-23",
                    16.39
                ],
                [
                    "2024-12-24",
                    7.88
                ],
                [
                    "2024-12-25",
                    8.83
                ],
                [
                    "2024-12-26",
                    -3.96
                ],
                [
                    "2024-12-27",
                    3.54
                ],
                [
                    "2024-12-30",
                    1.05
                ],
                [
                    "2024-12-31",
                    -0.96
                ],
                [
                    "2025-01-02",
                    9.17
                ],
                [
                    "2025-01-03",
                    1.96
                ],
                [
                    "2025-01-06",
                    4.97
                ],
                [
                    "2025-01-07",
                    -4.31
                ],
                [
                    "2025-01-08",
                    -0.96
                ],
                [
                    "2025-01-09",
                    -4.1
                ],
                [
                    "2025-01-10",
                    2.64
                ],
                [
                    "2025-01-13",
                    3.49
                ],
                [
                    "2025-01-14",
                    -3.1
                ],
                [
                    "2025-01-15",
                    -2.96
                ],
                [
                    "2025-01-16",
                    7.63
                ],
                [
                    "2025-01-17",
                    -3.26
                ],
                [
                    "2025-01-20",
                    0.03
                ],
                [
                    "2025-01-21",
                    6.81
                ],
                [
                    "2025-01-22",
                    9.45
                ],
                [
                    "2025-01-23",
                    4.04
                ],
                [
                    "2025-01-24",
                    10.96
                ],
                [
                    "2025-01-27",
                    2.87
                ],
                [
                    "2025-02-05",
                    -2.44
                ],
                [
                    "2025-02-06",
                    3.88
                ],
                [
                    "2025-02-07",
                    0.12
                ],
                [
                    "2025-02-10",
                    12.02
                ],
                [
                    "2025-02-11",
                    9.79
                ],
                [
                    "2025-02-12",
                    12.96
                ],
                [
                    "2025-02-13",
                    1.98
                ],
                [
                    "2025-02-14",
                    13.28
                ],
                [
                    "2025-02-17",
                    8.93
                ],
                [
                    "2025-02-18",
                    12.81
                ],
                [
                    "2025-02-19",
                    7.13
                ],
                [
                    "2025-02-20",
                    1.15
                ],
                [
                    "2025-02-21",
                    4.67
                ],
                [
                    "2025-02-24",
                    0.67
                ],
                [
                    "2025-02-25",
                    12.92
                ],
                [
                    "2025-02-26",
                    11.22
                ],
                [
                    "2025-02-27",
                    1.09
                ],
                [
                    "2025-02-28",
                    -3.64
                ],
                [
                    "2025-03-03",
                    3.16
                ],
                [
                    "2025-03-04",
                    -4.83
                ],
                [
                    "2025-03-05",
                    7.93
                ],
                [
                    "2025-03-06",
                    10.7
                ],
                [
                    "2025-03-07",
                    -1.4
                ],
                [
                    "2025-03-10",
                    4.03
                ],
                [
                    "2025-03-11",
                    -7.35
                ],
                [
                    "2025-03-12",
                    -1.11
                ],
                [
                    "2025-03-13",
                    4.19
                ],
                [
                    "2025-03-14",
                    -0.65
                ],
                [
                    "2025-03-17",
                    -6.03
                ],
                [
                    "2025-03-18",
                    8.17
                ],
                [
                    "2025-03-19",
                    16.02
                ],
                [
                    "2025-03-20",
                    -1.98
                ],
                [
                    "2025-03-21",
                    -1.55
                ],
                [
                    "2025-03-24",
                    -4.85
                ],
                [
                    "2025-03-25",
                    2.17
                ],
                [
                    "2025-03-26",
                    -0.82
                ],
                [
                    "2025-03-27",
                    -6.14
                ],
                [
                    "2025-03-28",
                    -1.47
                ],
                [
                    "2025-03-31",
                    -4.73
                ],
                [
                    "2025-04-01",
                    11.83
                ],
                [
                    "2025-04-02",
                    -9.92
                ],
                [
                    "2025-04-03",
                    -2.21
                ],
                [
                    "2025-04-07",
                    18.46
                ],
                [
                    "2025-04-08",
                    12.45
                ],
                [
                    "2025-04-09",
                    -7.32
                ],
                [
                    "2025-04-10",
                    2.47
                ],
                [
                    "2025-04-11",
                    5.01
                ],
                [
                    "2025-04-14",
                    -2.48
                ],
                [
                    "2025-04-15",
                    7.12
                ],
                [
                    "2025-04-16",
                    5.7
                ],
                [
                    "2025-04-17",
                    -0.34
                ],
                [
                    "2025-04-18",
                    9.48
                ],
                [
                    "2025-04-21",
                    4.11
                ],
                [
                    "2025-04-22",
                    11.55
                ],
                [
                    "2025-04-23",
                    12.6
                ],
                [
                    "2025-04-24",
                    14.44
                ],
                [
                    "2025-04-25",
                    -6.78
                ],
                [
                    "2025-04-28",
                    -0.74
                ],
                [
                    "2025-04-29",
                    0.58
                ],
                [
                    "2025-04-30",
                    2.81
                ],
                [
                    "2025-05-06",
                    2.37
                ],
                [
                    "2025-05-07",
                    -1.05
                ],
                [
                    "2025-05-08",
                    -3.0
                ],
                [
                    "2025-05-09",
                    5.82
                ],
                [
                    "2025-05-12",
                    -2.22
                ],
                [
                    "2025-05-13",
                    3.25
                ],
                [
                    "2025-05-14",
                    1.54
                ],
                [
                    "2025-05-15",
                    5.09
                ],
                [
                    "2025-05-16",
                    -1.06
                ],
                [
                    "2025-05-19",
                    -1.36
                ],
                [
                    "2025-05-20",
                    6.65
                ],
                [
                    "2025-05-21",
                    -6.12
                ],
                [
                    "2025-05-22",
                    -3.24
                ],
                [
                    "2025-05-23",
                    5.26
                ],
                [
                    "2025-05-26",
                    0.4
                ],
                [
                    "2025-05-27",
                    7.2
                ],
                [
                    "2025-05-28",
                    6.99
                ],
                [
                    "2025-05-29",
                    3.32
                ],
                [
                    "2025-05-30",
                    -1.22
                ],
                [
                    "2025-06-03",
                    -0.73
                ],
                [
                    "2025-06-04",
                    -3.96
                ],
                [
                    "2025-06-05",
                    -4.84
                ],
                [
                    "2025-06-06",
                    0.49
                ],
                [
                    "2025-06-09",
                    -0.91
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-17",
                    2.12
                ],
                [
                    "2025-03-26",
                    3.81
                ],
                [
                    "2025-05-22",
                    3.56
                ],
                [
                    "2025-06-05",
                    5.12
                ],
                [
                    "2025-06-06",
                    -5.75
                ],
                [
                    "2025-06-09",
                    10.8
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-01-09",
                    -1.93
                ],
                [
                    "2025-01-10",
                    1.45
                ],
                [
                    "2025-01-13",
                    0.12
                ],
                [
                    "2025-01-14",
                    -1.58
                ],
                [
                    "2025-01-15",
                    6.36
                ],
                [
                    "2025-01-20",
                    -2.57
                ],
                [
                    "2025-03-13",
                    5.33
                ],
                [
                    "2025-03-14",
                    2.73
                ],
                [
                    "2025-03-17",
                    3.91
                ],
                [
                    "2025-03-26",
                    -2.98
                ],
                [
                    "2025-03-27",
                    11.29
                ],
                [
                    "2025-03-28",
                    7.9
                ],
                [
                    "2025-03-31",
                    12.58
                ],
                [
                    "2025-04-01",
                    -6.94
                ],
                [
                    "2025-04-02",
                    3.95
                ],
                [
                    "2025-04-03",
                    6.25
                ],
                [
                    "2025-05-06",
                    2.74
                ],
                [
                    "2025-06-06",
                    5.26
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2024-12-11",
                    0.89
                ],
                [
                    "2024-12-12",
                    -0.48
                ],
                [
                    "2024-12-13",
                    7.28
                ],
                [
                    "2024-12-16",
                    -0.94
                ],
                [
                    "2024-12-17",
                    1.76
                ],
                [
                    "2024-12-18",
                    2.86
                ],
                [
                    "2024-12-19",
                    9.73
                ],
                [
                    "2024-12-20",
                    3.4
                ],
                [
                    "2024-12-23",
                    16.39
                ],
                [
                    "2024-12-24",
                    7.88
                ],
                [
                    "2024-12-25",
                    8.83
                ],
                [
                    "2024-12-26",
                    -3.96
                ],
                [
                    "2024-12-27",
                    3.54
                ],
                [
                    "2024-12-30",
                    1.05
                ],
                [
                    "2024-12-31",
                    -0.96
                ],
                [
                    "2025-01-02",
                    9.17
                ],
                [
                    "2025-01-03",
                    1.96
                ],
                [
                    "2025-01-06",
                    4.97
                ],
                [
                    "2025-01-07",
                    -4.31
                ],
                [
                    "2025-01-08",
                    -0.96
                ],
                [
                    "2025-01-16",
                    7.63
                ],
                [
                    "2025-01-17",
                    -3.26
                ],
                [
                    "2025-01-21",
                    6.81
                ],
                [
                    "2025-01-22",
                    9.45
                ],
                [
                    "2025-01-23",
                    4.04
                ],
                [
                    "2025-01-24",
                    10.96
                ],
                [
                    "2025-01-27",
                    2.87
                ],
                [
                    "2025-02-05",
                    -2.44
                ],
                [
                    "2025-02-06",
                    3.88
                ],
                [
                    "2025-02-07",
                    0.12
                ],
                [
                    "2025-02-10",
                    12.02
                ],
                [
                    "2025-02-11",
                    9.79
                ],
                [
                    "2025-02-12",
                    12.96
                ],
                [
                    "2025-02-13",
                    1.98
                ],
                [
                    "2025-02-14",
                    13.28
                ],
                [
                    "2025-02-17",
                    8.93
                ],
                [
                    "2025-02-18",
                    12.81
                ],
                [
                    "2025-02-19",
                    7.13
                ],
                [
                    "2025-02-20",
                    1.15
                ],
                [
                    "2025-02-21",
                    4.67
                ],
                [
                    "2025-02-24",
                    0.67
                ],
                [
                    "2025-02-25",
                    12.92
                ],
                [
                    "2025-02-26",
                    11.22
                ],
                [
                    "2025-02-27",
                    1.09
                ],
                [
                    "2025-02-28",
                    -3.64
                ],
                [
                    "2025-03-03",
                    3.16
                ],
                [
                    "2025-03-04",
                    -4.83
                ],
                [
                    "2025-03-05",
                    7.93
                ],
                [
                    "2025-03-06",
                    10.7
                ],
                [
                    "2025-03-07",
                    -1.4
                ],
                [
                    "2025-03-10",
                    4.03
                ],
                [
                    "2025-03-11",
                    -7.35
                ],
                [
                    "2025-03-12",
                    -1.11
                ],
                [
                    "2025-03-18",
                    8.17
                ],
                [
                    "2025-03-19",
                    16.02
                ],
                [
                    "2025-03-20",
                    -1.98
                ],
                [
                    "2025-03-21",
                    -1.55
                ],
                [
                    "2025-03-24",
                    -4.85
                ],
                [
                    "2025-03-25",
                    2.17
                ],
                [
                    "2025-04-07",
                    18.46
                ],
                [
                    "2025-04-08",
                    12.45
                ],
                [
                    "2025-04-09",
                    -7.32
                ],
                [
                    "2025-04-10",
                    2.47
                ],
                [
                    "2025-04-11",
                    5.01
                ],
                [
                    "2025-04-14",
                    -2.48
                ],
                [
                    "2025-04-15",
                    7.12
                ],
                [
                    "2025-04-16",
                    5.7
                ],
                [
                    "2025-04-17",
                    -0.34
                ],
                [
                    "2025-04-18",
                    9.48
                ],
                [
                    "2025-04-21",
                    4.11
                ],
                [
                    "2025-04-22",
                    11.55
                ],
                [
                    "2025-04-23",
                    12.6
                ],
                [
                    "2025-04-24",
                    14.44
                ],
                [
                    "2025-04-25",
                    -6.78
                ],
                [
                    "2025-04-28",
                    -0.74
                ],
                [
                    "2025-04-29",
                    0.58
                ],
                [
                    "2025-04-30",
                    2.81
                ],
                [
                    "2025-05-07",
                    -1.05
                ],
                [
                    "2025-05-09",
                    5.82
                ],
                [
                    "2025-05-14",
                    1.54
                ],
                [
                    "2025-05-15",
                    5.09
                ],
                [
                    "2025-05-16",
                    -1.06
                ],
                [
                    "2025-05-19",
                    -1.36
                ],
                [
                    "2025-05-20",
                    6.65
                ],
                [
                    "2025-05-26",
                    0.4
                ],
                [
                    "2025-05-27",
                    7.2
                ],
                [
                    "2025-05-28",
                    6.99
                ],
                [
                    "2025-05-29",
                    3.32
                ],
                [
                    "2025-05-30",
                    -1.22
                ],
                [
                    "2025-06-03",
                    -0.73
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-02",
                "2024-12-03",
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600760 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_2d819b9d37cc4d0b9b9ab835caf3118d.setOption(option_2d819b9d37cc4d0b9b9ab835caf3118d);
            window.addEventListener('resize', function(){
                chart_2d819b9d37cc4d0b9b9ab835caf3118d.resize();
            })
    </script>

<script>
// 等待页面完全加载
window.addEventListener('load', function() {
    setTimeout(function() {
        // 获取所有图表实例
        var charts = [];
        var chartDoms = document.querySelectorAll('[_echarts_instance_]');
        chartDoms.forEach(function(dom) {
            var chart = echarts.getInstanceByDom(dom);
            if (chart) {
                charts.push(chart);
            }
        });

        console.log('找到图表数量:', charts.length);

        if (charts.length >= 2) {
            // 确保缩放联动
            echarts.connect(charts);
            console.log('缩放联动已设置');

            var chart1 = charts[0];
            var chart2 = charts[1];

            // 简单的鼠标悬停联动
            var isUpdating = false;

            chart1.on('mousemove', function(params) {
                if (!isUpdating && params.dataIndex !== undefined) {
                    isUpdating = true;
                    chart2.dispatchAction({
                        type: 'showTip',
                        seriesIndex: 0,
                        dataIndex: params.dataIndex
                    });
                    setTimeout(function() { isUpdating = false; }, 10);
                }
            });

            chart2.on('mousemove', function(params) {
                if (!isUpdating && params.dataIndex !== undefined) {
                    isUpdating = true;
                    chart1.dispatchAction({
                        type: 'showTip',
                        seriesIndex: 0,
                        dataIndex: params.dataIndex
                    });
                    setTimeout(function() { isUpdating = false; }, 10);
                }
            });

            console.log('鼠标悬停联动已设置');
        }
    }, 2000);
});
</script>

</body>
</html>
